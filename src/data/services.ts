import { Calendar, Mic, MessageSquare, Video, Users } from "lucide-react";
import { LucideIcon } from "lucide-react";

export interface ServiceItem {
  icon: LucideIcon;
  title: string;
  description: string;
}

export const serviceItems: ServiceItem[] = [
  {
    icon: Calendar,
    title: "Sự kiện <PERSON>hi<PERSON>",
    description: "",
  },
  {
    icon: Mic,
    title: "Sự kiện Cưới",
    description: "",
  },
  {
    icon: MessageSquare,
    title: "Trao giải thưởng",
    description: "",
  },
  {
    icon: Video,
    title: "Sự kiện Ra mắt",
    description: "",
  },
  {
    icon: Users,
    title: "Talk show",
    description: "",
  },
];
