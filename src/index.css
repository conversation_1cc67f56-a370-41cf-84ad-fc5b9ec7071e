@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 47 36% 95%; /* #faf5e5 */
    --foreground: 240 10% 3.9%;

    --card: 47 36% 95%; /* #faf5e5 */
    --card-foreground: 240 10% 3.9%;

    --popover: 47 36% 95%; /* #faf5e5 */
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 6% 10%;
    --primary-foreground: 47 36% 95%; /* #faf5e5 */

    --secondary: 240 5% 90%;
    --secondary-foreground: 240 6% 10%;

    --muted: 240 5% 90%;
    --muted-foreground: 240 4% 46%;

    --accent: 240 5% 90%;
    --accent-foreground: 240 6% 10%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 6% 85%;
    --input: 240 6% 85%;
    --ring: 240 10% 3.9%;

    --radius: 0.75rem;

    --sidebar-background: 47 36% 93%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 47 36% 95%;
    --sidebar-accent: 240 5% 90%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 80%;
    --sidebar-ring: 224 76% 48%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased overflow-x-hidden;
    font-family: "Inter", sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Playfair Display", serif;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/70 backdrop-blur-md border border-white/20 shadow-sm;
  }

  .section-title {
    @apply text-4xl md:text-5xl font-medium mb-2 tracking-tight;
  }

  .section-subtitle {
    @apply text-sm uppercase tracking-widest text-muted-foreground font-semibold mb-6;
  }

  .card-hover {
    @apply transition duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  .nav-item {
    @apply relative px-3 py-2 text-foreground/80 hover:text-foreground transition-colors duration-200 ease-in-out;
  }

  .nav-item::after {
    @apply content-[''] absolute w-0 h-[2px] bg-primary left-1/2 -translate-x-1/2 bottom-0 transition-all duration-300 ease-in-out;
  }

  .nav-item:hover::after {
    @apply w-full;
  }

  .max-container {
    @apply max-w-[1440px] mx-auto px-4 sm:px-6 md:px-16 w-full;
  }
}

.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
