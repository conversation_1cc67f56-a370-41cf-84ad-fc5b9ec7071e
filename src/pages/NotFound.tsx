
import { useLocation } from "react-router-dom";
import { useEffect } from "react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div className="text-center max-w-md px-6">
        <h1 className="text-6xl md:text-8xl font-bold mb-4">404</h1>
        <p className="text-xl text-muted-foreground mb-8">The page you are looking for doesn't exist or has been moved.</p>
        <a 
          href="/" 
          className="inline-block bg-primary text-primary-foreground px-8 py-4 rounded-full shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1"
        >
          Return Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;
