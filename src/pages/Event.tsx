import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { ArrowLeft, X } from "lucide-react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { portfolioItems } from "@/data/portfolioItems";
import Footer from "@/components/Footer";

const Event = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const event = portfolioItems.find((item) => item.id === Number(id));

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-medium mb-4">Không tìm thấy sự kiện</h1>
          <button
            onClick={() => navigate("/")}
            className="bg-primary text-primary-foreground px-6 py-3 rounded-full"
          >
            Trở về Trang chủ
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 bg-background/80 backdrop-blur-md border-b border-border z-40">
        <div className="max-container py-4">
          <button
            onClick={() => navigate("/")}
            className="flex items-center gap-2 text-foreground hover:text-primary transition-colors"
          >
            <ArrowLeft size={20} />
            Trở về Trang chủ
          </button>
        </div>
      </div>

      {/* Event Content */}
      <div className="max-container py-12">
        {/* Event Header */}
        <div className="mb-12">
          <div className="flex items-center gap-3 mb-4">
            <span className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm">
              {event.category}
            </span>
          </div>
          <h1 className="section-title mb-6">{event.title}</h1>
          <p className="text-lg text-muted-foreground mb-6 max-w-3xl">
            {event.description}
          </p>
          <div className="prose prose-lg max-w-4xl">
            <p className="text-foreground leading-relaxed">
              {event.fullDescription}
            </p>
          </div>
        </div>

        {/* Image Gallery */}
        <div className="mb-12 border-1 border-b pb-32">
          <h2 className="text-2xl font-medium mb-8">Thư viện ảnh</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {event.images.map((image, index) => (
              <div
                key={index}
                className="aspect-[4/3] overflow-hidden rounded-lg cursor-pointer group"
                onClick={() => setSelectedImage(image)}
              >
                <img
                  src={image}
                  alt={`${event.title} - Image ${index + 1}`}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center border-1">
          <h3 className="text-3xl font-medium mb-4">
            Bạn muốn sự kiện thật ấn tượng?
          </h3>
          <p className="text-muted-foreground mb-6">
            Hãy để Thuỳ đồng hành cùng bạn tạo nên khoảnh khắc đáng nhớ.
          </p>
          <a
            href="#contact"
            onClick={() => navigate("/#contact")}
            className="bg-primary text-primary-foreground px-8 py-4 rounded-full shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1 inline-block"
          >
            Liên hệ
          </a>
        </div>
      </div>

      {/* Image Modal */}
      <Dialog
        open={!!selectedImage}
        onOpenChange={() => setSelectedImage(null)}
      >
        <DialogContent className="max-w-4xl w-full p-0 overflow-hidden">
          {selectedImage && (
            <div className="relative">
              <img
                src={selectedImage}
                alt={event.title}
                className="w-full h-auto max-h-[80vh] object-contain"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
      <Footer />
    </div>
  );
};

export default Event;
