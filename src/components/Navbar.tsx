import { useState, useEffect } from "react";
import { Menu } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const NavItems = () => (
    <>
      {/* <a href="#about" className="nav-item">About</a> */}
      <a href="/" className="nav-item hover:text-primary transition-colors">Trang chủ</a>
      <a href="#portfolio" className="nav-item hover:text-primary transition-colors">Portfolio</a>
      <a href="#brands" className="nav-item">Đồng hành</a>
      <a href="#testimonials" className="nav-item hover:text-primary transition-colors">Testimonials</a>
      {/* <a href="#contact" className="nav-item">Contact</a> */}
    </>
  );

  return (
    <nav
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${isScrolled
        ? "bg-white/80 backdrop-blur-md shadow-sm py-4"
        : "bg-transparent py-6"
        }`}
    >
      <div className="max-container flex justify-between items-center">
        <h1 className="text-xl md:text-2xl tracking-[0.25em] font-light">Minh Thuỳ</h1>

        {/* Desktop Navigation */}
        <div className="hidden md:flex space-x-6 items-center">
          <NavItems />
        </div>

        {/* Mobile Navigation */}
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              aria-label="Open menu"
            >
              <Menu className="h-5 w-5 md:h-6 md:w-6" />
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="w-[280px] sm:w-[320px]">
            <nav className="flex flex-col gap-4 mt-8">
              <NavItems />
            </nav>
          </SheetContent>
        </Sheet>
      </div>
    </nav>
  );
};

export default Navbar;
