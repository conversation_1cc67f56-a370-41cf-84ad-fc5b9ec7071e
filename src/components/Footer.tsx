import { useState, useEffect } from "react";
import { FacebookIcon, YoutubeIcon } from "lucide-react";

const navLinks1 = [
  { label: "Order Books", href: "#order-books" },
  { label: "About", href: "#about" },
  { label: "Contact", href: "#contact" },
  { label: "Blog", href: "#blog" },
];
const navLinks2 = [
  { label: "Courses", href: "#courses" },
  { label: "Meet & Greet", href: "#meet-greet" },
  { label: "Patreon Club", href: "#patreon" },
];

const Footer = () => {
  const [year, setYear] = useState(new Date().getFullYear());
  useEffect(() => { setYear(new Date().getFullYear()); }, []);

  return (
    <footer className="bg-[#f7f5ee] rounded-b-xl border-t border-[#e5ded6] text-[#222] text-base">
      <div className="max-w-7xl mx-auto px-6 pt-12 pb-4">
        <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-10 md:gap-0">
          {/* Left: Title */}
          <div className="md:w-1/3 flex flex-col items-center md:items-start mb-8 md:mb-0">
            <h1 className="text-2xl tracking-[0.25em] font-light mb-2 text-center md:text-left">Minh Thuỳ</h1>
            {/* short description */}
            <p className="text-sm text-center md:text-left">
              MC Chuyên nghiệp, cuốn hút và phong cách.
              <br />
              Tạo nên những dấu ấn khó phai cho từng khoảnh khắc
            </p>
            {/* add youtube button and facebook button here*/}
            <div className="flex items-center gap-4 mt-4">
              <a
                href="https://www.youtube.com/@minhthuybui05"
                target="_blank"
                rel="noopener noreferrer"
                className="w-12 h-12 flex items-center justify-center rounded-full bg-[#b6a98c] hover:bg-[#8c7a5a] transition-colors duration-200 focus:outline-none"
                aria-label="YouTube"
                tabIndex={0}
                onKeyDown={e => { if (e.key === 'Enter') window.open('https://www.youtube.com/@minhthuyevent', '_blank'); }}
              >
                <YoutubeIcon className="w-6 h-6 text-white" />
              </a>
              <a
                href="https://www.facebook.com/sibui05"
                target="_blank"
                rel="noopener noreferrer"
                className="w-12 h-12 flex items-center justify-center rounded-full bg-[#b6a98c] hover:bg-[#8c7a5a] transition-colors duration-200 focus:outline-none"
                aria-label="Facebook"
                tabIndex={0}
                onKeyDown={e => { if (e.key === 'Enter') window.open('https://www.facebook.com/minhthuyevent', '_blank'); }}
              >
                <FacebookIcon className="w-6 h-6 text-white" />
              </a>
            </div>
          </div>

          {/* Right: Newsletter & Info */}
          <div className="md:w-1/3 flex flex-col items-center md:items-end">
            <div className="w-full max-w-xs mb-4">
              <p className="mb-2 text-center md:text-left">Nhận thông tin mới nhất</p>
              <form className="flex items-center border border-[#e5ded6] rounded-md bg-white px-4 py-2">
                <input
                  type="email"
                  placeholder="Điền email của bạn"
                  className="flex-1 bg-transparent outline-none text-sm placeholder:text-[#b6a98c]"
                  aria-label="Điền email của bạn"
                />
                <button
                  type="submit"
                  className="ml-2 text-[#b6a98c] hover:text-[#8c7a5a] focus:text-[#8c7a5a] outline-none"
                  aria-label="Subscribe"
                  tabIndex={0}
                  onKeyDown={e => { if (e.key === 'Enter') e.currentTarget.click(); }}
                >
                  <svg width="22" height="22" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" viewBox="0 0 24 24"><rect x="2" y="4" width="20" height="16" rx="2" /><path d="M22 6 12 13 2 6" /></svg>
                </button>
              </form>
            </div>
            {/* <div className="text-right md:text-left text-sm mt-2">
              <div className="mb-1">Email</div>
              <div>All sales are final.</div>
            </div> */}
          </div>
        </div>
        {/* Bottom bar */}
        <div className="mt-12 text-center pt-4 border-t border-[#e5ded6] text-[#8c7a5a] text-sm">
          <div>
            Developed by <span className="font-bold">Silver Team</span> @ {year}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
