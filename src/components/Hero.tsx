import { useEffect, useState } from "react";
import { ArrowDown } from "lucide-react";

const Hero = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(true);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) {
        setShowScrollButton(false);
      } else {
        setShowScrollButton(true);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background image */}
      {/* <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat -z-20"
        style={{
          backgroundImage: "url('/images/event2/2.jpg')"
        }}
      ></div> */}

      {/* Background overlay */}
      <div className="absolute inset-0 bg-background/60 dark:bg-background/70 -z-10"></div>

      {/* Content container */}
      <div className="max-container relative z-10 pt-20 px-4 flex flex-col items-center text-center">
        {/* Animated subtitle */}
        <p
          className={`text-xs sm:text-sm md:text-base uppercase tracking-widest text-muted-foreground font-medium mb-4 opacity-0 ${isVisible ? "animate-fade-in" : ""
            }`}
        >
          {/* Professional Host & Master of Ceremonies */}
          MC Sự kiện chuyên nghiệp
        </p>

        {/* Animated title */}
        <h1
          className={`text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-6 opacity-0 leading-tight text-[#524439] ${isVisible ? "animate-fade-in animate-delay-200" : ""
            }`}
        >
          {/* Nâng tầm <br className="sm:hidden" />
          <span className="italic">Sự kiện</span> */}
          Nâng tầm Sự kiện
        </h1>

        {/* Animated description */}
        <p
          className={`text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mb-10 px-4 opacity-0 ${isVisible ? "animate-fade-in animate-delay-300" : ""
            }`}
        >
          {/* Captivating audiences with charisma, wit, and professionalism.
          Creating memorable moments that leave lasting impressions. */}
          Thu hút khán giả bằng sự thiết tha, tự tin và chuyên nghiệp.
          Tạo nên những khoảnh khắc đáng nhớ và ấn tượng.
        </p>

        {/* Call to action buttons */}
        <div
          className={`flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto opacity-0 ${isVisible ? "animate-fade-in animate-delay-400" : ""
            }`}
        >
          <a
            href="#portfolio"
            className="bg-primary text-primary-foreground px-6 sm:px-8 py-3 sm:py-4 rounded-full shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1 text-center"
          >
            Xem Portfolio
          </a>
        </div>
      </div>

      {/* Scroll indicator - fixed to bottom of viewport */}
      <div
        className={`fixed bottom-6 sm:bottom-10 left-1/2 -translate-x-1/2 z-20 transition-all duration-300 ${isVisible && showScrollButton ? "opacity-100" : "opacity-0"
          }`}
      >
        <div className="flex flex-col items-center">
          <span className="text-xs sm:text-sm text-muted-foreground mb-2">Cuộn xuống</span>
          <ArrowDown className="animate-float" size={20} />
        </div>
      </div>
    </section>
  );
};

export default Hero;