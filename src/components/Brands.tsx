"use client";

import { useEffect, useState } from "react";
import {
    Carousel,
    CarouselApi,
    CarouselContent,
    CarouselItem,
} from "@/components/ui/carousel";

const brands = [
    {
        name: 'Thông tin truyền thông',
        logo: '/images/brands/thongtintruyenthong-logo.jpg',
    },
    {
        name: 'Pharmacity',
        logo: '/images/brands/pharmacity-logo.jpg',
    },
    {
        name: 'VNISA',
        logo: '/images/brands/vnisa-logo.jpg',
    },
    {
        name: 'Hacoocha',
        logo: '/images/brands/hacoocha-logo.jpg',
    },
    {
        name: 'Hoa Sen',
        logo: '/images/brands/hoasen-logo.jpg',
    },
    {
        name: 'Bộ Công An',
        logo: '/images/brands/bocongan-logo.jpg',
    },
    {
        name: 'Thông tin truyền thông',
        logo: '/images/brands/thongtintruyenthong-logo.jpg',
    },
    {
        name: 'Pharmacity',
        logo: '/images/brands/pharmacity-logo.jpg',
    },
    {
        name: 'VNISA',
        logo: '/images/brands/vnisa-logo.jpg',
    },
    {
        name: 'Hacoocha',
        logo: '/images/brands/hacoocha-logo.jpg',
    },
    {
        name: 'Hoa Sen',
        logo: '/images/brands/hoasen-logo.jpg',
    },
];

const Brands = () => {
    const [api, setApi] = useState<CarouselApi>();
    const [current, setCurrent] = useState(0);

    useEffect(() => {
        if (!api) {
            return;
        }

        const interval = setInterval(() => {
            if (api.selectedScrollSnap() + 1 === api.scrollSnapList().length) {
                setCurrent(0);
                api.scrollTo(0);
            } else {
                api.scrollNext();
                setCurrent(current + 1);
            }
        }, 3000);

        return () => clearInterval(interval);
    }, [api, current]);

    return (
        <section id="brands" className="py-24 bg-gray-50">
            <div className="container mx-auto px-4">
                <h2 className="section-title text-center mb-12">Các đơn vị đã hợp tác</h2>
                <p className="max-w-2xl mx-auto text-muted-foreground mb-8 text-center">
                    Các đơn vị, đối tác và khách hàng đã luôn đồng hành cùng Minh Thùy trong nhiều sự kiện lớn nhỏ, góp phần tạo nên những dấu ấn khó quên.
                </p>
                <div className="relative">
                    <div className="bg-gradient-to-r from-gray-50 via-white/0 to-gray-50 z-10 absolute left-0 top-0 right-0 bottom-0 w-full h-full pointer-events-none"></div>
                    <Carousel setApi={setApi} className="w-full">
                        <CarouselContent>
                            {brands.map((brand, index) => (
                                <CarouselItem
                                    key={brand.name}
                                    className="basis-1/2 md:basis-1/3 lg:basis-1/6"
                                >
                                    <div className="relative w-full aspect-[3/2] transition-all duration-300">
                                        <img
                                            src={brand.logo}
                                            alt={`${brand.name} logo`}
                                            className="object-contain p-4"
                                        />
                                    </div>
                                </CarouselItem>
                            ))}
                        </CarouselContent>
                    </Carousel>
                </div>
            </div>
        </section>
    );
};

export default Brands;
