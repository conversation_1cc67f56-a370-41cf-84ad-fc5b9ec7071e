import { Mail, MapPin, Phone } from "lucide-react";

const Contact = () => {
  return (
    <section id="contact" className="bg-[#f6f2e7] py-24 md:py-32">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8 items-center">
          {/* Left: CONTACT label */}
          <div className="md:col-span-2 flex md:block justify-center md:justify-start mb-8 md:mb-0">
            <span className="tracking-widest text-sm uppercase text-zinc-700 md:mt-2 md:ml-2">Contact</span>
          </div>

          {/* Right: Main content */}
          <div className="md:col-span-3 flex flex-col items-center md:items-start text-center md:text-left">
            <h2 className="text-3xl md:text-5xl lg:text-6xl font-serif font-bold mb-8 text-zinc-900 leading-tight">
              Contact me to discuss your painting needs. <br className="hidden md:block" />
              Let's bring your dream artwork to life!
            </h2>
            <div className="space-y-4 text-lg w-full max-w-xl">
              <div className="flex items-center gap-3">
                <Phone className="w-6 h-6 text-zinc-700" aria-hidden="true" />
                <span className="text-zinc-800">************</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="w-6 h-6 text-zinc-700" aria-hidden="true" />
                <span className="text-zinc-800"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="w-6 h-6 text-zinc-700" aria-hidden="true" />
                <span className="text-zinc-800">13 Fifth Avenue, New York, NY 101660</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
