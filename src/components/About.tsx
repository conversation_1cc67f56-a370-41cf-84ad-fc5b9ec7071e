
import { useEffect, useRef, useState } from "react";

const About = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: 0.15,
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section id="about" className="py-24 md:py-32" ref={sectionRef}>
      <div className="max-container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 md:gap-24 items-center">
          {/* Image column */}
          <div 
            className={`opacity-0 ${
              isVisible ? "animate-fade-in" : ""
            }`}
          >
            <div className="relative">
              <div className="aspect-[3/4] rounded-lg overflow-hidden">
                <div className="w-full h-full bg-muted flex items-center justify-center">
                  <div className="text-lg text-muted-foreground">Professional Photo</div>
                </div>
              </div>
              <div className="absolute -bottom-6 -right-6 w-48 h-48 bg-primary/5 rounded-lg -z-10"></div>
            </div>
          </div>

          {/* Content column */}
          <div>
            <div 
              className={`opacity-0 ${
                isVisible ? "animate-fade-in animate-delay-200" : ""
              }`}
            >
              <p className="section-subtitle">About Me</p>
              <h2 className="section-title">Bringing Your Events to Life</h2>
              <p className="text-muted-foreground mb-6">
                With over a decade of experience as a professional MC and host, I've mastered the art of 
                audience engagement and event management across various platforms and occasions.
              </p>
            </div>

            <div 
              className={`grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8 opacity-0 ${
                isVisible ? "animate-fade-in animate-delay-300" : ""
              }`}
            >
              <div className="glass-card p-6 rounded-lg">
                <h3 className="text-xl font-medium mb-2">Experience</h3>
                <p className="text-muted-foreground">
                  10+ years hosting corporate events, galas, weddings, and conferences.
                </p>
              </div>
              <div className="glass-card p-6 rounded-lg">
                <h3 className="text-xl font-medium mb-2">Adaptability</h3>
                <p className="text-muted-foreground">
                  Quick-thinking with the ability to adapt to any audience or situation.
                </p>
              </div>
              <div className="glass-card p-6 rounded-lg">
                <h3 className="text-xl font-medium mb-2">Professionalism</h3>
                <p className="text-muted-foreground">
                  Meticulous preparation and flawless execution for every event.
                </p>
              </div>
              <div className="glass-card p-6 rounded-lg">
                <h3 className="text-xl font-medium mb-2">Charisma</h3>
                <p className="text-muted-foreground">
                  Natural ability to engage, entertain, and connect with diverse audiences.
                </p>
              </div>
            </div>

            <div 
              className={`opacity-0 ${
                isVisible ? "animate-fade-in animate-delay-400" : ""
              }`}
            >
              <a 
                href="#contact" 
                className="inline-flex items-center text-primary hover:underline font-medium"
              >
                Let's work together
                <svg 
                  className="ml-2" 
                  width="18" 
                  height="18" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
