import { useState, useRef, useEffect } from "react";
import { serviceItems } from "../data/services";

const Services = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: 0.1,
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section id="services" className="py-24 md:py-32" ref={sectionRef}>
      <div className="max-container">
        <div
          className={`text-center max-w-2xl mx-auto mb-16 opacity-0 ${isVisible ? "animate-fade-in" : ""
            }`}
        >
          <p className="section-subtitle">Services</p>
          <h2 className="section-title">What I Offer</h2>
          <p className="text-muted-foreground">
            From corporate events to intimate gatherings, I provide professional hosting services
            tailored to your specific needs and vision.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {serviceItems.map((item, index) => (
            <div
              key={index}
              className={`glass-card rounded-xl p-8 opacity-0 ${isVisible ? `animate-fade-in animate-delay-${(index % 6) * 100}` : ""
                }`}
            >
              <div className="text-primary mb-4">
                <item.icon size={32} />
              </div>
              <h3 className="text-xl font-medium mb-2">{item.title}</h3>
              <p className="text-muted-foreground">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
