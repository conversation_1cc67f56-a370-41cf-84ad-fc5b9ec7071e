import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Album } from "lucide-react";
import { portfolioItems, filters, PortfolioItem } from "../data/portfolioItems";

const Portfolio = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFilter, setActiveFilter] = useState("All");
  const sectionRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: 0.1,
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  const filteredItems = activeFilter === "All"
    ? portfolioItems
    : portfolioItems.filter(item => item.category === activeFilter);

  const handleProjectClick = (project: PortfolioItem) => {
    navigate(`/event/${project.id}`);
  };

  return (
    <section id="portfolio" className="py-24 md:py-32" ref={sectionRef}>
      <div className="max-container">
        <div
          className={`text-center max-w-2xl mx-auto mb-16 opacity-0 ${isVisible ? "animate-fade-in" : ""
            }`}
        >
          <p className="section-subtitle">Portfolio</p>
          <h2 className="section-title">Sự kiện nổi bật</h2>
          <p className="text-muted-foreground mb-8">
            Hãy cùng khám phá những sự kiện mà Minh Thuỳ vinh dự được đồng hành và dẫn dắt.
            Mỗi chương trình đều được thực hiện với sự tận tâm và chỉn chu đến từng chi tiết nhỏ, đem lại những trải nghiệm trọn vẹn và ngập tràn cảm hứng.
          </p>

          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {filters.map(filter => (
              <button
                key={filter}
                onClick={() => setActiveFilter(filter)}
                className={`px-4 py-2 rounded-full text-sm transition-all ${activeFilter === filter
                  ? "bg-primary text-primary-foreground"
                  : "bg-secondary text-foreground hover:bg-secondary/80"
                  }`}
              >
                {filter}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredItems.map((item, index) => (
            <div
              key={item.id}
              className={`group rounded-xl overflow-hidden card-hover opacity-0 cursor-pointer ${isVisible ? `animate-fade-in animate-delay-${(index % 6) * 100}` : ""
                }`}
              onClick={() => handleProjectClick(item)}
            >
              <div className="relative aspect-[4/3] overflow-hidden">
                <div className="w-full h-full bg-muted flex items-center justify-center">
                  <img
                    src={item.images[0]}
                    alt={item.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute inset-0 bg-primary/80 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-300">
                  <span className="text-primary-foreground font-medium">Xem sự kiện</span>
                </div>
              </div>
              <div className="p-6 bg-white border border-border">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-xl font-medium">{item.title}</h3>
                  <span className="text-xs bg-secondary px-2 py-1 rounded">{item.category}</span>
                </div>
                <p className="text-muted-foreground text-sm">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
