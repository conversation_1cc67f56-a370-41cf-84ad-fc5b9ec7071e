import { useState, useRef, useEffect } from "react";
import { testimonials } from "../data/testimonials";

const Testimonials = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: 0.1,
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Auto-rotate testimonials
    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section id="testimonials" className="py-24 md:py-32 bg-secondary/50" ref={sectionRef}>
      <div className="max-container">
        <div
          className={`text-center max-w-2xl mx-auto mb-16 opacity-0 ${isVisible ? "animate-fade-in" : ""
            }`}
        >
          <p className="section-subtitle">Testimonials</p>
          <h2 className="section-title">Cảm nhận của khách hàng</h2>
          <p className="text-muted-foreground">
            Tiếng nói của khách hàng là câu chuyện chân thật nhất về trải nghiệm khi hợp tác cùng tôi.
          </p>
        </div>

        <div
          className={`max-w-4xl mx-auto opacity-0 ${isVisible ? "animate-fade-in animate-delay-200" : ""
            }`}
        >
          <div className="relative h-[300px] md:h-[250px]">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-all duration-500 ease-in-out ${index === activeIndex
                  ? "opacity-100 translate-x-0"
                  : "opacity-0 translate-x-20 pointer-events-none"
                  }`}
              >
                <div className="glass-card rounded-xl p-8 md:p-12 h-full flex flex-col justify-center">
                  <div className="text-4xl text-primary mb-6">"</div>
                  <p className="text-lg md:text-xl italic mb-6">{testimonial.quote}</p>
                  <div>
                    <p className="font-medium">{testimonial.author}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.position}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 rounded-full transition-all ${index === activeIndex
                  ? "bg-primary scale-125"
                  : "bg-muted hover:bg-muted-foreground"
                  }`}
                aria-label={`View testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
