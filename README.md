# Personal Portfolio

A modern, responsive personal portfolio website built with React, TypeScript, and TailwindCSS. This portfolio showcases my work, skills, and experience in an elegant and professional manner.

## 🚀 Features

- 🎨 Modern and clean UI design
- 📱 Fully responsive layout
- ⚡ Fast and optimized performance
- 🌙 Dark/Light mode support
- ♿ Accessibility-first approach
- 🎯 SEO optimized
- 🛠️ Built with modern web technologies

## 🛠️ Tech Stack

- **Framework:** React 18
- **Language:** TypeScript
- **Styling:** TailwindCSS
- **UI Components:** Shadcn/UI (Radix UI)
- **Build Tool:** Vite
- **Form Handling:** React Hook Form
- **Data Fetching:** TanStack Query
- **Routing:** React Router DOM
- **Charts:** Recharts
- **Animations:** TailwindCSS Animate
- **Development:** ESLint, TypeScript

## 🏁 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/portfolio.git
cd portfolio
```

2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:5173](http://localhost:5173) in your browser.

## 📦 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## 🏗️ Project Structure

```
portfolio/
├── src/              # Source files
├── public/           # Static assets
├── components/       # React components
├── styles/          # Global styles
└── ...
```

## 🎨 Customization

1. Update personal information in the configuration files
2. Modify the theme colors in `tailwind.config.ts`
3. Add your projects and experiences
4. Customize components in the `components` directory

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Contact

Feel free to reach out to me through:

- Email: [<EMAIL>]
- LinkedIn: [Your LinkedIn Profile]
- Twitter: [@YourTwitterHandle]

---

Made with ❤️ by [Your Name]
